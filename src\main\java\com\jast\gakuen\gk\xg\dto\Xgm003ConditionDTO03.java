/*
 * Xgm003ConditionDTO03.java
 *
 * 著作権  ：Copyright Japan System Techniques Co., Ltd. All Rights Reserved.
 * 会社名  ：日本システム技術株式会社
 *
 */
package com.jast.gakuen.gk.xg.dto;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import javax.faces.model.SelectItem;

import com.jast.gakuen.core.common.BaseDTO;
import com.jast.gakuen.core.common.annotation.RxOpt;
import com.jast.gakuen.core.common.annotation.RxValidationForUI;
import com.jast.gakuen.gk.gh.dto.Ghd008DTO11;

import lombok.Getter;
import lombok.Setter;

/**
 * 納付金指定検索条件DTO
 *
 * <AUTHOR> System Techniques Co.,Ltd.
 */
@Getter
@Setter
public class Xgm003ConditionDTO03 extends BaseDTO {

	/**
	 * 割当年度
	 */
	@RxValidationForUI(id = "xgm003.wariateNendo.0.label", hissuFlg = true)
	protected Integer wariateNendo;

	/**
	 * 納付金種別
	 */
	@RxOpt(value = "noufuKinShubetsu")
	private List<String> noufuKinShubetsu;

	/**
	 * 業務コード
	 */
	@RxOpt(value = "gyomucd")
	private String gyomucd;
}
