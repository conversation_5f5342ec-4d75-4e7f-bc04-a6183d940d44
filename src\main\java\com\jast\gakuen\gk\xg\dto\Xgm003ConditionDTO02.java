/*
 * Xgm003ConditionDTO02.java
 *
 * 著作権  ：Copyright Japan System Techniques Co., Ltd. All Rights Reserved.
 * 会社名  ：日本システム技術株式会社
 *
 */
package com.jast.gakuen.gk.xg.dto;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import javax.faces.model.SelectItem;

import com.jast.gakuen.core.common.BaseDTO;
import com.jast.gakuen.core.common.annotation.RxOpt;
import com.jast.gakuen.core.common.annotation.RxValidationForUI;
import com.jast.gakuen.gk.gh.dto.Ghd008DTO11;

import lombok.Getter;
import lombok.Setter;

/**
 * 納付金指定検索条件DTO
 *
 * <AUTHOR> System Techniques Co.,Ltd.
 */
@Getter
@Setter
public class Xgm003ConditionDTO02 extends BaseDTO {

	/**
	 * 割当年度
	 */
	@RxValidationForUI(id = "ghePaywBun.nendo.0.label", hissuFlg = true)
	protected Integer wariateNendo;

	/**
	 * 納付金種別
	 */
	@RxOpt(value = "noufuKinShubetsu")
	private List<String> noufuKinShubetsu;

	/**
	 * 業務コード
	 */
	@RxOpt(value = "gyomucd")
	private String gyomucd;

	/**
	 * 発行対象状況区分（全額未納）
	 */
	@RxOpt(value = "t1_hakkoTgtZengakuMino")
	protected boolean hakkoTgtZengakuMino;

	/**
	 * 発行対象状況区分（一部）
	 */
	@RxOpt(value = "t1_hakkoTgtIchibuMino")
	protected boolean hakkoTgtIchibuMino;

	/** ######出力対象学生条件エリア###### **/
	/**
	 * 管轄
	 */
	@RxOpt(value = "manageStu")
	private List<String> manageStuList = new ArrayList();

	/**
	 * 共通管轄学生チェックによる活性制御
	 * 
	 * @return 学費管轄学チェックによる活性制御
	 */
	public boolean getManageStuCommon() {
		return manageStuList.contains("1");
	}

	/**
	 * 学費管轄学チェックによる活性制御
	 * 
	 * @return 学費管轄学チェックによる活性制御
	 */
	public boolean getManageStuTuition() {
		return manageStuList.contains("2");
	}

	/**
	 * 入学年度
	 */
	@RxValidationForUI(id = "common.nyugakNendo.0.label")
	@RxOpt(value = "t1_nyugakNendo")
	protected Integer nyugakNendo;

	/**
	 * 入学学期NO
	 */
	@RxValidationForUI(id = "common.nyugakGakkiNo.0.label")
	@RxOpt(value = "t1_nyugakGakkiNo")
	protected Integer nyugakGakkiNo;

	/**
	 * 学生管理部署
	 */
	@RxOpt(value = "t1_gbushoCd")
	protected String gbushoCd;

	/**
	 * 所属学科組織
	 */
	@RxOpt(value = "t1_sgksCd")
	protected String sgksCd;

	/**
	 * 学費所属学科組織
	 */
	@RxOpt(value = "t1_ghSgksCd")
	protected String ghSgksCd;

	/**
	 * みなし入学年度
	 */
	@RxValidationForUI(id = "pkbGakIdo.nyugakNendoDeemed.0.label")
	@RxOpt(value = "t1_nyugakNendoDeemed")
	protected Integer nyugakNendoDeemed;

	/**
	 * みなし入学学期NO
	 */
	@RxValidationForUI(id = "pkbGakIdo.nyugakGakkiNoDeemed.0.label")
	@RxOpt(value = "t1_nyugakGakkiNoDeemed")
	protected Integer nyugakGakkiNoDeemed;

	/**
	 * カリキュラム学科組織
	 */
	@RxOpt(value = "t1_cgksCd")
	protected String cgksCd;

	/**
	 * 学年
	 */
	@RxOpt(value = "t1_gaknen")
	protected Integer gaknen;

	/**
	 * セメスタ
	 */
	@RxOpt(value = "t1_semester")
	protected Integer semester;

	/**
	 * 入学種別
	 */
	@RxOpt(value = "t1_nyugakSbtCd")
	protected String nyugakSbtCd;

	/**
	 * 就学種別
	 */
	@RxOpt(value = "t1_shugakSbtCd")
	protected String shugakSbtCd;

	/**
	 * 専攻コース種別
	 */
	@RxOpt(value = "t1_majorCourseSbtCd")
	protected String majorCourseSbtCd;

	/**
	 * 専攻コースコンボリスト
	 */
	protected List<SelectItem> majorCourseCdList;

	/**
	 * 専攻コース
	 */
	@RxOpt(value = "t1_majorCourseCd")
	protected String majorCourseCd;

	/**
	 * クラス種別
	 */
	@RxOpt(value = "t1_classSbtCd")
	protected String classSbtCd;

	/**
	 * クラスコンボリスト
	 */
	protected List<SelectItem> classCdList;

	/**
	 * クラス
	 */
	@RxOpt(value = "t1_classCd")
	protected String classCd;

	/**
	 * 留学生対象
	 */
	@RxOpt(value = "t1_ryugakseiFlg")
	protected Boolean ryugakseiFlg;

	/**
	 * 留学生区分
	 */
	@RxOpt(value = "t1_ryugakseiKbn")
	protected String ryugakseiKbn;

	/**
	 * 障害者対象
	 */
	@RxOpt(value = "t1_shogaisyaFlg")
	protected Boolean shogaisyaFlg;

	/**
	 * 社会人対象
	 */
	@RxOpt(value = "t1_shakaijinFlg")
	protected Boolean shakaijinFlg;

	/**
	 * 共通学生自由設定項目リスト
	 */
	@RxOpt(value = "t1_pkGkfrList")
	protected List<Ghd008DTO11> pkGkfrList = new ArrayList<>();

	/**
	 * 学費学生自由設定項目リスト
	 */
	@RxOpt(value = "t1_ghGkfrList")
	protected List<Ghd008DTO11> ghGkfrList = new ArrayList<>();

	/**
	 * 出身校コード
	 */
	@RxValidationForUI(id = "pkbGakNyushi.shshnkCd.0.label")
	@RxOpt(value = "t1_shshnkCd")
	protected String shshnkCd;

	/**
	 * 異動期間FROM
	 */
	@RxValidationForUI(id = "common.yidoKikanFrom.0.label")
	@RxOpt(value = "t1_idoKikanFrom")
	protected Date idoKikanFrom;

	/**
	 * 異動期間TO
	 */
	@RxValidationForUI(id = "common.yidoKikanTo.0.label")
	@RxOpt(value = "t1_idoKikanTo")
	protected Date idoKikanTo;

	/**
	 * 異動出学申請結果区分リスト
	 */
	@RxOpt(value = "t1_idoShutgakApplyKekkaKbnList")
	protected List<String> idoShutgakApplyKekkaKbnList = new ArrayList<>();

	/**
	 * 異動出学種別区分リスト
	 */
	@RxOpt(value = "t1_idoShutgakSbtKbnList")
	protected List<String> idoShutgakSbtKbnList = new ArrayList<>();
}
